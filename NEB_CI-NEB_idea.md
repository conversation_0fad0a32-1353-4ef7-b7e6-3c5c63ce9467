# NEB and CI-NEB for VibroML

### The Basic NEB Algorithm

The core idea of NEB is to optimize a series of intermediate structures, called "images," that connect the initial and final states. The optimization is special because it only allows the images to relax perpendicular to the path, while artificial "springs" keep them evenly spaced along the path.

Here is the algorithm:

1.  **Initialization:**

      * Define your initial state ($\\vec{R}\_0$) and final state ($\\vec{R}\_N$). These are fixed and do not change.
      * Create $N-1$ intermediate images ($\\vec{R}\_1, \\vec{R}*2, ..., \\vec{R}*{N-1}$) by linearly interpolating the atomic positions between $\\vec{R}\_0$ and $\\vec{R}\_N$. This initial chain of images is your "band."

2.  **Optimization Loop:** Repeat until the forces on the images are below a convergence threshold:

      * For each intermediate image $\\vec{R}\_i$ (where $i = 1, ..., N-1$):
          * **a. Calculate True Force:** Compute the true force from your potential energy function, $\\vec{F}\_i^{true} = -\\nabla E(\\vec{R}\_i)$.
          * **b. Estimate Path Tangent:** Determine the tangent to the path ($\\hat{\\tau}\_i$) at image $i$. A common way is to use the vector pointing from the previous image to the next one, but more accurate tangent estimations exist.
          * **c. Calculate Spring Force:** Compute the artificial spring force, $\\vec{F}*i^{spring} = k(|\\vec{R}*{i+1} - \\vec{R}\_i| - |\\vec{R}*i - \\vec{R}*{i-1}|)\\hat{\\tau}\_i$, where $k$ is the spring constant. This force pulls the images to be equally spaced.
          * **d. Project and Combine Forces:** This is the "nudging" step.
              * Project the true force perpendicular to the tangent: $\\vec{F}\_i^{\\perp} = \\vec{F}\_i^{true} - (\\vec{F}\_i^{true} \\cdot \\hat{\\tau}\_i)\\hat{\\tau}\_i$.
              * Project the spring force parallel to the tangent: $\\vec{F}\_i^{s, |} = (\\vec{F}\_i^{spring} \\cdot \\hat{\\tau}\_i)\\hat{\\tau}\_i$.
              * The total **NEB force** is the sum of these two projected components: $\\vec{F}\_i^{NEB} = \\vec{F}\_i^{\\perp} + \\vec{F}\_i^{s, |}$.
          * **e. Update Positions:** Move the atoms in image $\\vec{R}\_i$ according to the NEB force using an optimizer (e.g., steepest descent, L-BFGS). For steepest descent, this would be $\\vec{R}\_i \\leftarrow \\vec{R}\_i + \\delta t \\cdot \\vec{F}\_i^{NEB}$, where $\\delta t$ is a small time step.

3.  **Termination:** The loop ends when the magnitude of the largest $\\vec{F}\_i^{NEB}$ across all images is smaller than your defined tolerance.

-----

### The Climbing Image NEB (CI-NEB) Algorithm

CI-NEB is a simple but powerful modification to the basic algorithm that gives you a much more accurate estimate of the saddle point (the transition state) without needing a high density of images.

The algorithm is identical to the standard NEB, with one crucial change to the force calculation for a single "climbing" image.

1.  **Run Standard NEB:** Perform several iterations of the basic NEB algorithm described above until the path has started to relax and settle.

2.  **Identify the Climber:** Find the image with the highest potential energy along the current path. Let's call this image $\\vec{R}\_{climb}$.

3.  **Modify the Force for the Climber:** In all subsequent optimization steps, apply a modified force calculation **only to the climbing image**:

      * Calculate the true force $\\vec{F}*{climb}^{true}$ and the tangent $\\hat{\\tau}*{climb}$ as usual.
      * Instead of removing the parallel component of the true force, you **invert it**.
      * The **CI-NEB force** for the climbing image is:
        $\\vec{F}*{climb}^{CI-NEB} = \\vec{F}*{climb}^{true} - 2(\\vec{F}*{climb}^{true} \\cdot \\hat{\\tau}*{climb})\\hat{\\tau}\_{climb}$.
      * Effectively, this force pushes the image *uphill* along the path tangent but lets it relax *downhill* in all perpendicular directions. This drives it precisely to the saddle point.
      * **Crucially, you turn off the spring force for the climbing image.**

4.  **Continue Optimization:** All other images ($\\vec{R}\_i$ where $i \\neq climb$) continue to be updated using the standard NEB force ($\\vec{F}\_i^{NEB}$). The loop continues until convergence.

-----

### Python Implementation Pseudocode

Here’s a skeleton of what this might look like in Python:

```python
def run_ci_neb(images, potential, max_iterations, threshold, spring_k):
    """
    A simplified pseudocode implementation of CI-NEB.
    'images' is a list of atomic configurations [R_0, R_1, ..., R_N].
    """
    
    # Run a few standard NEB iterations first (e.g., 10-20% of max_iterations)
    # ... code for initial standard NEB run ...
    
    for i in range(max_iterations):
        
        # 1. Find the image with the highest energy to be the climber
        energies = [potential.get_energy(img) for img in images[1:-1]]
        climbing_image_index = energies.index(max(energies)) + 1

        max_force_magnitude = 0.0
        
        # Loop over intermediate images to calculate forces
        for j in range(1, len(images) - 1):
            
            # 2. Calculate true force and tangent
            true_force = potential.get_force(images[j])
            tangent = calculate_tangent(images[j-1], images[j], images[j+1])
            
            if j == climbing_image_index:
                # 3. Apply the CLIMBING force calculation
                force_parallel_component = np.dot(true_force, tangent) * tangent
                neb_force = true_force - 2 * force_parallel_component
            else:
                # 4. Apply the STANDARD NEB force calculation
                force_perp_component = true_force - np.dot(true_force, tangent) * tangent
                
                # Spring force (simplified)
                spring_force = calculate_spring_force(images[j-1], images[j], images[j+1], tangent, spring_k)
                
                neb_force = force_perp_component + spring_force
            
            # Update the max force for convergence check
            max_force_magnitude = max(max_force_magnitude, np.linalg.norm(neb_force))
            
            # Store force for this image (don't update positions yet)
            # ...
            
        # 5. Check for convergence
        if max_force_magnitude < threshold:
            print("NEB has converged!")
            break
            
        # 6. Update all image positions simultaneously using an optimizer
        # optimizer.step(images, forces)

    return images
```