#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, '.')

print("Testing minimal NEB functionality...")

try:
    # Test import
    from vibroml.utils.neb_utils import linear_interpolate_structures
    print("SUCCESS: NEB utils imported")
    
    # Test CLI
    from vibroml.utils.utils import get_arg_parser_and_settings
    parser, settings = get_arg_parser_and_settings()
    print("SUCCESS: Parser created")
    
    # Check method choices
    for action in parser._actions:
        if action.dest == 'method':
            print(f"Available methods: {action.choices}")
            if 'neb' in action.choices:
                print("SUCCESS: NEB method found in choices")
            else:
                print("ERROR: NEB method not found")
            break
    
    print("All basic tests passed!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
